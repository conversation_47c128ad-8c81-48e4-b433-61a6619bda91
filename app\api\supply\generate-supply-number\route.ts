import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// دالة توليد رقم SUP فريد
async function generateUniqueId(tx: any, table: string, prefix: string): Promise<string> {
  let attempts = 0;
  const maxAttempts = 10;

  while (attempts < maxAttempts) {
    try {
      // الحصول على أكبر رقم موجود في قاعدة البيانات
      const lastRecord = await tx[table].findFirst({
        where: {
          supplyOrderId: {
            startsWith: prefix
          }
        },
        orderBy: {
          supplyOrderId: 'desc'
        }
      });

      let nextNumber = 1;
      if (lastRecord && lastRecord.supplyOrderId) {
        const currentNumber = parseInt(lastRecord.supplyOrderId.replace(prefix, ''));
        if (!isNaN(currentNumber)) {
          nextNumber = currentNumber + 1;
        }
      }

      const newId = `${prefix}${nextNumber}`;

      // التحقق من عدم وجود الرقم (للتأكد)
      const existing = await tx[table].findUnique({
        where: { supplyOrderId: newId }
      });

      if (!existing) {
        return newId;
      }

      attempts++;
    } catch (error) {
      console.error(`Attempt ${attempts + 1} failed:`, error);
      attempts++;
    }
  }

  // إذا فشلت جميع المحاولات، استخدم timestamp
  const timestamp = Date.now().toString().slice(-6);
  return `${prefix}${timestamp}`;
}

export async function POST(request: NextRequest) {
  try {
    const supplyNumber = await prisma.$transaction(async (tx) => {
      return await generateUniqueId(tx, 'supplyOrder', 'SUP-');
    });

    return NextResponse.json({ supplyNumber });
  } catch (error) {
    console.error('خطأ في توليد رقم أمر التوريد:', error);
    return NextResponse.json(
      { error: 'فشل في توليد رقم أمر التوريد' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
